"""
Gestionnaire Gemma 3n via Ollama - Adaptação para uso exclusivo do Ollama
Substitui o GemmaHandlerV2 para usar o modelo gemma3n:e4b via API Ollama
"""
import json
import time
import logging
import requests
import base64
from io import BytesIO
from PIL import Image
from typing import Dict, List, Optional, Union
import numpy as np
import cv2

from config.settings import OLLAMA_CONFIG

logger = logging.getLogger(__name__)

class GemmaOllamaHandler:
    """Gestionnaire pour Gemma 3n via Ollama API"""
    
    def __init__(self):
        self.base_url = OLLAMA_CONFIG['base_url']
        self.model_name = OLLAMA_CONFIG['model_name']
        self.timeout = OLLAMA_CONFIG['timeout']
        self.max_retries = OLLAMA_CONFIG['max_retries']
        self.retry_delay = OLLAMA_CONFIG['retry_delay']
        
        self.initialized = False
        self.ready = False
        
        logger.info(f"Initializing Gemma Ollama Handler")
        logger.info(f"Base URL: {self.base_url}")
        logger.info(f"Model: {self.model_name}")
        
        # Verificar disponibilidade do Ollama
        self._check_ollama_availability()
    
    def _check_ollama_availability(self):
        """Verifica se o Ollama está rodando e o modelo está disponível"""
        try:
            # Verificar se o serviço Ollama está rodando
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code != 200:
                logger.error(f"Ollama service not responding: {response.status_code}")
                return False
            
            # Verificar se o modelo está disponível
            models = response.json().get('models', [])
            model_available = any(
                model.get('name', '').startswith(self.model_name) 
                for model in models
            )
            
            if not model_available:
                logger.warning(f"Model {self.model_name} not found in Ollama")
                logger.info("Available models:")
                for model in models:
                    logger.info(f"  - {model.get('name', 'unknown')}")
                return False
            
            logger.info(f"✅ Ollama service running and model {self.model_name} available")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to connect to Ollama: {e}")
            return False
        except Exception as e:
            logger.error(f"Error checking Ollama availability: {e}")
            return False
    
    @property
    def model_available(self):
        """Propriedade para compatibilidade com GemmaHandlerV2"""
        return self._check_ollama_availability()
    
    def initialize_local_model(self):
        """Inicializa a conexão com Ollama (compatibilidade com interface anterior)"""
        try:
            logger.info("🔄 Initializing Ollama connection...")
            
            if not self._check_ollama_availability():
                logger.error("Cannot initialize - Ollama service or model not available")
                return False
            
            # Teste de funcionalidade
            test_result = self._test_model_functionality()
            
            if test_result:
                self.initialized = True
                self.ready = True
                logger.info("✅ Ollama connection successfully initialized and ready")
                return True
            else:
                logger.error("Ollama model test failed")
                return False
                
        except Exception as e:
            logger.error(f"Failed to initialize Ollama connection: {e}")
            self.initialized = False
            self.ready = False
            return False
    
    def _test_model_functionality(self):
        """Testa a funcionalidade básica do modelo"""
        try:
            logger.info("Testing Ollama model functionality...")
            
            test_prompt = "Respond with 'OK' if you can understand this message."
            
            response = self._make_ollama_request(test_prompt)
            
            if response and 'OK' in response.get('response', '').upper():
                logger.info("✅ Model test successful")
                return True
            else:
                logger.warning(f"Model test response: {response}")
                return False
                
        except Exception as e:
            logger.error(f"Model test failed: {e}")
            return False
    
    def is_ready(self):
        """Verifica se o handler está pronto para análise"""
        return self.ready and self.initialized
    
    def analyze_eye_regions(self, eye_regions: List[Dict], confidence_threshold: float = 0.5) -> Dict:
        """Analisa múltiplas regiões oculares com Gemma 3n via Ollama"""
        if not self.is_ready():
            logger.warning("Ollama model not ready, using fallback analysis")
            return self._fallback_analysis(eye_regions)
        
        try:
            results = {
                'regions_analyzed': len(eye_regions),
                'method': 'gemma3n_ollama',
                'results': [],
                'processing_time': 0
            }
            
            start_time = time.time()
            
            for i, region in enumerate(eye_regions):
                logger.info(f"Analyzing region {i+1}/{len(eye_regions)} via Ollama")
                
                # Analisar cada região
                region_result = self._analyze_single_region(region, confidence_threshold)
                region_result['region_id'] = i
                results['results'].append(region_result)
            
            results['processing_time'] = time.time() - start_time
            logger.info(f"Ollama analysis completed in {results['processing_time']:.2f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"Ollama analysis failed: {e}")
            return self._fallback_analysis(eye_regions)
    
    def _analyze_single_region(self, region: Dict, confidence_threshold: float) -> Dict:
        """Analisa uma única região ocular via Ollama"""
        try:
            # Extrair dados da região
            region_image = region.get('image')
            region_type = region.get('type', 'unknown')
            
            if region_image is None:
                return self._create_error_result("No image data in region")
            
            # Preparar imagem para análise
            processed_image = self._preprocess_image_for_analysis(region_image)
            
            # Extrair características visuais
            visual_features = self._extract_visual_features(processed_image)
            
            # Criar prompt médico
            prompt = self._create_medical_prompt(region_type, visual_features)
            
            # Fazer análise via Ollama
            response = self._make_ollama_request(prompt)
            
            if response:
                # Processar resposta
                result = self._parse_medical_response(response.get('response', ''))
                result['analysis_method'] = 'ollama_text_with_cv_features'
                result['visual_features_used'] = True
                return result
            else:
                return self._create_error_result("Ollama request failed")
                
        except Exception as e:
            logger.error(f"Single region analysis failed: {e}")
            return self._create_error_result(f"Analysis error: {e}")
    
    def _make_ollama_request(self, prompt: str, image_data: Optional[str] = None) -> Optional[Dict]:
        """Faz uma requisição para a API do Ollama"""
        for attempt in range(self.max_retries):
            try:
                payload = {
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": OLLAMA_CONFIG['stream'],
                    "options": {
                        "temperature": OLLAMA_CONFIG['temperature'],
                        "top_p": OLLAMA_CONFIG['top_p'],
                        "top_k": OLLAMA_CONFIG['top_k'],
                        "num_predict": OLLAMA_CONFIG['max_tokens']
                    }
                }
                
                # Adicionar imagem se fornecida (para modelos multimodais)
                if image_data:
                    payload["images"] = [image_data]
                
                response = requests.post(
                    f"{self.base_url}/api/generate",
                    json=payload,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.warning(f"Ollama request failed with status {response.status_code}: {response.text}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"Ollama request timeout (attempt {attempt + 1}/{self.max_retries})")
            except requests.exceptions.RequestException as e:
                logger.warning(f"Ollama request error (attempt {attempt + 1}/{self.max_retries}): {e}")
            except Exception as e:
                logger.error(f"Unexpected error in Ollama request: {e}")
                break
            
            if attempt < self.max_retries - 1:
                time.sleep(self.retry_delay)
        
        logger.error("All Ollama request attempts failed")
        return None

    def _preprocess_image_for_analysis(self, image):
        """Preprocessa imagem para análise"""
        try:
            if isinstance(image, np.ndarray):
                image = Image.fromarray(image)
            elif not isinstance(image, Image.Image):
                logger.warning("Invalid image format, attempting conversion")
                return None

            # Redimensionar se muito grande
            max_size = 512
            if max(image.size) > max_size:
                ratio = max_size / max(image.size)
                new_size = tuple(int(dim * ratio) for dim in image.size)
                image = image.resize(new_size, Image.Resampling.LANCZOS)

            # Converter para RGB se necessário
            if image.mode != 'RGB':
                image = image.convert('RGB')

            return image

        except Exception as e:
            logger.error(f"Image preprocessing failed: {e}")
            return None

    def _extract_visual_features(self, image: Image.Image) -> str:
        """Extrai características visuais da imagem para análise textual"""
        try:
            # Converter para array numpy
            image_array = np.array(image)
            gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)

            # Análises básicas
            features = {
                "brightness": {
                    "mean": float(np.mean(gray)),
                    "std": float(np.std(gray)),
                    "max": float(np.max(gray)),
                    "min": float(np.min(gray))
                },
                "image_size": f"{image.width}x{image.height}"
            }

            # Detecção de círculos (pupillas potenciais)
            circles = cv2.HoughCircles(
                gray, cv2.HOUGH_GRADIENT, 1, 20,
                param1=30, param2=20, minRadius=3, maxRadius=min(gray.shape)//3
            )

            pupil_analysis = {}
            leukocoria_indicators = []

            if circles is not None:
                circles = np.round(circles[0, :]).astype("int")
                pupil_analysis["circles_detected"] = len(circles)

                # Analisar cada círculo detectado
                for i, (x, y, r) in enumerate(circles):
                    mask = np.zeros(gray.shape, dtype=np.uint8)
                    cv2.circle(mask, (x, y), r, 255, -1)
                    pupil_region = gray[mask > 0]

                    if len(pupil_region) > 0:
                        pupil_brightness = float(np.mean(pupil_region))
                        pupil_std = float(np.std(pupil_region))

                        # Indicadores de leucocoria
                        brightness_threshold = 100  # Ajustável
                        if pupil_brightness > brightness_threshold:
                            leukocoria_indicators.append({
                                "circle_id": i,
                                "brightness": pupil_brightness,
                                "position": f"({x}, {y})",
                                "radius": r,
                                "warning": "Bright pupil detected - possible leukocoria"
                            })

                        pupil_analysis[f"circle_{i}"] = {
                            "position": f"({x}, {y})",
                            "radius": r,
                            "brightness": pupil_brightness,
                            "brightness_std": pupil_std
                        }
            else:
                pupil_analysis["circles_detected"] = 0
                pupil_analysis["note"] = "No circular structures detected"

            # Análise de contraste
            contrast_analysis = {
                "overall_contrast": float(np.std(gray)),
                "brightness_distribution": {
                    "dark_pixels_percent": float(np.sum(gray < 50) / gray.size * 100),
                    "bright_pixels_percent": float(np.sum(gray > 200) / gray.size * 100),
                    "medium_pixels_percent": float(np.sum((gray >= 50) & (gray <= 200)) / gray.size * 100)
                }
            }

            # Compilar features em texto descritivo
            features_text = f"""
VISUAL ANALYSIS REPORT:
- Image dimensions: {features['image_size']}
- Overall brightness: {features['brightness']['mean']:.1f} (std: {features['brightness']['std']:.1f})
- Brightness range: {features['brightness']['min']:.0f} to {features['brightness']['max']:.0f}
- Contrast level: {contrast_analysis['overall_contrast']:.1f}
- Dark pixels: {contrast_analysis['brightness_distribution']['dark_pixels_percent']:.1f}%
- Bright pixels: {contrast_analysis['brightness_distribution']['bright_pixels_percent']:.1f}%

PUPIL DETECTION:
- Circular structures found: {pupil_analysis['circles_detected']}
"""

            if pupil_analysis['circles_detected'] > 0:
                for key, value in pupil_analysis.items():
                    if key.startswith('circle_'):
                        features_text += f"- {key}: brightness {value['brightness']:.1f}, position {value['position']}, radius {value['radius']}\n"

            if leukocoria_indicators:
                features_text += "\nLEUKOCORIA INDICATORS:\n"
                for indicator in leukocoria_indicators:
                    features_text += f"- {indicator['warning']} (brightness: {indicator['brightness']:.1f})\n"

            return features_text.strip()

        except Exception as e:
            logger.error(f"Visual features extraction failed: {e}")
            return f"Error extracting visual features: {e}"

    def _create_medical_prompt(self, region_type: str, visual_features: str) -> str:
        """Cria prompt médico para análise via Ollama"""
        return f"""<start_of_turn>user
You are a specialized medical AI assistant for retinoblastoma detection in children.

MEDICAL CONTEXT:
- Retinoblastoma is a serious eye cancer affecting children (usually under 6 years)
- Main early sign: leukocoria (white pupil reflex) visible in flash photographs
- Early detection can save lives (95% survival rate vs 30% when late)
- Region type: {region_type}

PATIENT SAFETY: This analysis is for a child's eye health. Be conservative.

VISUAL ANALYSIS DATA:
{visual_features}

MEDICAL TASK:
Based on the visual characteristics above, analyze for retinoblastoma signs.

OUTPUT (JSON format):
{{
    "leukocoria_detected": boolean,
    "confidence": float (0-100),
    "risk_level": "low|medium|high",
    "pupil_description": "clinical description",
    "medical_reasoning": "detailed analysis",
    "recommendations": "specific medical advice",
    "urgency": "routine|soon|urgent|immediate"
}}

Remember: Err on the side of caution for child safety.
<end_of_turn>
<start_of_turn>model
"""

    def _parse_medical_response(self, response_text: str) -> Dict:
        """Processa resposta médica do modelo"""
        try:
            # Tentar extrair JSON da resposta
            import re

            # Procurar por JSON na resposta
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                try:
                    result = json.loads(json_str)

                    # Validar campos obrigatórios
                    required_fields = ['leukocoria_detected', 'confidence', 'risk_level']
                    for field in required_fields:
                        if field not in result:
                            result[field] = self._get_default_value(field)

                    # Garantir tipos corretos
                    result['leukocoria_detected'] = bool(result.get('leukocoria_detected', False))
                    result['confidence'] = float(result.get('confidence', 0))
                    result['risk_level'] = str(result.get('risk_level', 'low'))

                    return result

                except json.JSONDecodeError:
                    logger.warning("Failed to parse JSON from model response")

            # Fallback: análise textual da resposta
            return self._parse_text_response(response_text)

        except Exception as e:
            logger.error(f"Failed to parse medical response: {e}")
            return self._create_error_result(f"Response parsing error: {e}")

    def _parse_text_response(self, text: str) -> Dict:
        """Analisa resposta textual quando JSON não está disponível"""
        text_lower = text.lower()

        # Detectar indicadores de leucocoria
        leukocoria_keywords = ['leukocoria', 'white pupil', 'bright pupil', 'abnormal reflection']
        leukocoria_detected = any(keyword in text_lower for keyword in leukocoria_keywords)

        # Estimar confiança baseada em palavras-chave
        confidence_keywords = ['confident', 'certain', 'clear', 'obvious', 'definite']
        uncertainty_keywords = ['uncertain', 'unclear', 'possible', 'maybe', 'might']

        confidence = 50  # Base
        if any(keyword in text_lower for keyword in confidence_keywords):
            confidence += 30
        if any(keyword in text_lower for keyword in uncertainty_keywords):
            confidence -= 20

        confidence = max(0, min(100, confidence))

        # Determinar nível de risco
        risk_level = 'low'
        if leukocoria_detected:
            if confidence > 70:
                risk_level = 'high'
            elif confidence > 40:
                risk_level = 'medium'

        return {
            'leukocoria_detected': leukocoria_detected,
            'confidence': confidence,
            'risk_level': risk_level,
            'pupil_description': 'Analyzed from text response',
            'medical_reasoning': text[:200] + '...' if len(text) > 200 else text,
            'recommendations': 'Consult ophthalmologist if concerns persist',
            'urgency': 'soon' if leukocoria_detected else 'routine'
        }

    def _get_default_value(self, field: str):
        """Retorna valores padrão para campos obrigatórios"""
        defaults = {
            'leukocoria_detected': False,
            'confidence': 0,
            'risk_level': 'low',
            'pupil_description': 'No description available',
            'medical_reasoning': 'Analysis incomplete',
            'recommendations': 'Consult medical professional',
            'urgency': 'routine'
        }
        return defaults.get(field, None)

    def _create_error_result(self, error_message: str) -> Dict:
        """Cria resultado de erro padronizado"""
        return {
            'leukocoria_detected': False,
            'confidence': 0,
            'risk_level': 'low',
            'pupil_description': 'Analysis failed',
            'medical_reasoning': f'Error: {error_message}',
            'recommendations': 'Technical error - please retry or consult medical professional',
            'urgency': 'routine',
            'error': True,
            'error_message': error_message
        }

    def _fallback_analysis(self, eye_regions: List[Dict]) -> Dict:
        """Análise de fallback quando Ollama não está disponível"""
        logger.info("Using fallback analysis (Ollama not available)")

        results = {
            'regions_analyzed': len(eye_regions),
            'method': 'fallback_cv_only',
            'results': [],
            'processing_time': 0
        }

        start_time = time.time()

        for i, region in enumerate(eye_regions):
            # Análise básica usando apenas computer vision
            fallback_result = {
                'region_id': i,
                'leukocoria_detected': False,
                'confidence': 10,  # Baixa confiança
                'risk_level': 'low',
                'pupil_description': 'Basic computer vision analysis only',
                'medical_reasoning': 'Ollama service unavailable - limited analysis performed',
                'recommendations': 'Please ensure Ollama service is running and retry analysis',
                'urgency': 'routine',
                'analysis_method': 'fallback_cv_only',
                'fallback': True
            }

            results['results'].append(fallback_result)

        results['processing_time'] = time.time() - start_time
        return results
