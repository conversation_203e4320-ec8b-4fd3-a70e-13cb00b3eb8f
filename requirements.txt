# RetinoG - Requirements for Google Gemma Worldwide Hackathon
# Installation: pip install -r requirements.txt

# Core ML libraries
torch>=2.0.0
torchvision>=0.15.0
transformers>=4.35.0
accelerate>=0.24.0

# Google AI (optionnel)
google-generativeai>=0.3.0

# Computer Vision
opencv-python>=4.8.0
mediapipe>=0.10.7
face-recognition>=1.3.0
dlib>=19.24.0

# Image processing
Pillow>=10.0.0
numpy>=1.24.0
scipy>=1.11.0

# GUI and visualization  
matplotlib>=3.7.0
seaborn>=0.12.0

# Utilities
python-dotenv>=1.0.0
tqdm>=4.65.0
requests>=2.31.0
psutil>=5.9.0
kaggle>=1.5.16

# Development tools (optionnel)
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

# Gemma 3n optimizations
timm>=1.0.17
safetensors>=0.4.0
bitsanbytes>=0.46.1

# Mobile optimizations (pour déploiement futur)
# onnx>=1.14.0
# torch-mobile
