# RetinoG - Requirements for Google Gemma Worldwide Hackathon
# Installation: pip install -r requirements.txt

# Ollama integration for Gemma 3n
ollama>=0.3.0
requests>=2.31.0
httpx>=0.25.0

# Google AI (optionnel)
google-generativeai>=0.3.0

# Computer Vision
opencv-python>=4.8.0
mediapipe>=0.10.7
face-recognition>=1.3.0
dlib>=19.24.0

# Image processing
Pillow>=10.0.0
numpy>=1.24.0
scipy>=1.11.0

# GUI and visualization  
matplotlib>=3.7.0
seaborn>=0.12.0

# Utilities
python-dotenv>=1.0.0
tqdm>=4.65.0
psutil>=5.9.0

# Development tools (optionnel)
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

# JSON and data handling
pydantic>=2.0.0
typing-extensions>=4.0.0

# Mobile optimizations (pour déploiement futur)
# onnx>=1.14.0
# torch-mobile
