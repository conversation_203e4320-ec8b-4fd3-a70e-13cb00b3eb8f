"""
Script de configuração do Ollama para RetinoblastoGemma
Verifica instalação do Ollama e configura o modelo gemma3n:e4b
"""
import subprocess
import requests
import time
import logging
import sys
import json
from pathlib import Path

# Adicionar o diretório pai ao path para imports
sys.path.append(str(Path(__file__).parent.parent))

try:
    from config.settings import OLLAMA_CONFIG
except ImportError:
    # Fallback se config não estiver acessível
    OLLAMA_CONFIG = {
        'base_url': 'http://localhost:11434',
        'model_name': 'gemma3n:e4b',
        'timeout': 120
    }

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OllamaSetup:
    def __init__(self):
        self.base_url = OLLAMA_CONFIG['base_url']
        self.model_name = OLLAMA_CONFIG['model_name']
        self.timeout = OLLAMA_CONFIG['timeout']
    
    def check_ollama_installation(self):
        """Verifica se o Ollama está instalado"""
        try:
            result = subprocess.run(['ollama', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.strip()
                logger.info(f"✅ Ollama encontrado: {version}")
                return True
            else:
                logger.error("❌ Ollama não está instalado ou não está no PATH")
                return False
        except subprocess.TimeoutExpired:
            logger.error("❌ Timeout ao verificar versão do Ollama")
            return False
        except FileNotFoundError:
            logger.error("❌ Comando 'ollama' não encontrado")
            return False
        except Exception as e:
            logger.error(f"❌ Erro ao verificar Ollama: {e}")
            return False
    
    def check_ollama_service(self):
        """Verifica se o serviço Ollama está rodando"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                logger.info("✅ Serviço Ollama está rodando")
                return True
            else:
                logger.error(f"❌ Serviço Ollama retornou status {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            logger.error("❌ Não foi possível conectar ao serviço Ollama")
            return False
        except requests.exceptions.Timeout:
            logger.error("❌ Timeout ao conectar com Ollama")
            return False
        except Exception as e:
            logger.error(f"❌ Erro ao verificar serviço Ollama: {e}")
            return False
    
    def start_ollama_service(self):
        """Tenta iniciar o serviço Ollama"""
        try:
            logger.info("🔄 Tentando iniciar o serviço Ollama...")
            
            # Tentar iniciar o serviço em background
            subprocess.Popen(['ollama', 'serve'], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL)
            
            # Aguardar alguns segundos para o serviço iniciar
            time.sleep(5)
            
            # Verificar se o serviço está rodando
            if self.check_ollama_service():
                logger.info("✅ Serviço Ollama iniciado com sucesso")
                return True
            else:
                logger.error("❌ Falha ao iniciar serviço Ollama")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao iniciar serviço Ollama: {e}")
            return False
    
    def list_available_models(self):
        """Lista modelos disponíveis no Ollama"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get('models', [])
                logger.info(f"📋 Modelos disponíveis no Ollama ({len(models)}):")
                for model in models:
                    name = model.get('name', 'unknown')
                    size = model.get('size', 0)
                    size_gb = size / (1024**3) if size > 0 else 0
                    logger.info(f"  - {name} ({size_gb:.1f} GB)")
                return models
            else:
                logger.error(f"❌ Erro ao listar modelos: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"❌ Erro ao listar modelos: {e}")
            return []
    
    def check_model_availability(self):
        """Verifica se o modelo gemma3n:e4b está disponível"""
        models = self.list_available_models()
        
        for model in models:
            model_name = model.get('name', '')
            if model_name.startswith(self.model_name):
                logger.info(f"✅ Modelo {self.model_name} encontrado: {model_name}")
                return True
        
        logger.warning(f"⚠️ Modelo {self.model_name} não encontrado")
        return False
    
    def pull_model(self):
        """Baixa o modelo gemma3n:e4b"""
        try:
            logger.info(f"🔄 Baixando modelo {self.model_name}...")
            logger.info("⏳ Este processo pode levar vários minutos dependendo da conexão...")
            
            # Usar subprocess para mostrar progresso em tempo real
            process = subprocess.Popen(
                ['ollama', 'pull', self.model_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Mostrar output em tempo real
            for line in process.stdout:
                print(line.strip())
            
            process.wait()
            
            if process.returncode == 0:
                logger.info(f"✅ Modelo {self.model_name} baixado com sucesso")
                return True
            else:
                logger.error(f"❌ Falha ao baixar modelo {self.model_name}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao baixar modelo: {e}")
            return False
    
    def test_model(self):
        """Testa o modelo com uma requisição simples"""
        try:
            logger.info(f"🧪 Testando modelo {self.model_name}...")
            
            payload = {
                "model": self.model_name,
                "prompt": "Respond with 'OK' if you can understand this message.",
                "stream": False
            }
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '').strip()
                
                if 'OK' in response_text.upper():
                    logger.info("✅ Teste do modelo bem-sucedido")
                    return True
                else:
                    logger.warning(f"⚠️ Resposta inesperada do modelo: {response_text}")
                    return False
            else:
                logger.error(f"❌ Erro no teste do modelo: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao testar modelo: {e}")
            return False
    
    def setup_complete_environment(self):
        """Configura o ambiente completo do Ollama"""
        logger.info("🚀 Iniciando configuração completa do Ollama para RetinoblastoGemma")
        logger.info("=" * 60)
        
        # 1. Verificar instalação do Ollama
        if not self.check_ollama_installation():
            logger.error("❌ Ollama não está instalado")
            logger.info("💡 Instale o Ollama em: https://ollama.ai")
            return False
        
        # 2. Verificar/iniciar serviço
        if not self.check_ollama_service():
            logger.info("🔄 Serviço não está rodando, tentando iniciar...")
            if not self.start_ollama_service():
                logger.error("❌ Não foi possível iniciar o serviço Ollama")
                logger.info("💡 Tente executar manualmente: ollama serve")
                return False
        
        # 3. Verificar modelo
        if not self.check_model_availability():
            logger.info(f"🔄 Modelo {self.model_name} não encontrado, baixando...")
            if not self.pull_model():
                logger.error("❌ Falha ao baixar o modelo")
                return False
        
        # 4. Testar modelo
        if not self.test_model():
            logger.error("❌ Falha no teste do modelo")
            return False
        
        logger.info("✅ Configuração do Ollama concluída com sucesso!")
        logger.info("🎯 RetinoblastoGemma está pronto para usar o Ollama")
        return True

def main():
    """Função principal"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Setup Ollama para RetinoblastoGemma")
    parser.add_argument("--check-only", action="store_true", 
                       help="Apenas verificar status sem fazer alterações")
    parser.add_argument("--pull-model", action="store_true",
                       help="Forçar download do modelo")
    parser.add_argument("--test-only", action="store_true",
                       help="Apenas testar modelo existente")
    
    args = parser.parse_args()
    
    setup = OllamaSetup()
    
    try:
        if args.check_only:
            print("🔍 Verificando status do Ollama...")
            setup.check_ollama_installation()
            setup.check_ollama_service()
            setup.check_model_availability()
            
        elif args.pull_model:
            print("📥 Baixando modelo...")
            if setup.check_ollama_service() or setup.start_ollama_service():
                setup.pull_model()
            else:
                print("❌ Serviço Ollama não está disponível")
                
        elif args.test_only:
            print("🧪 Testando modelo...")
            setup.test_model()
            
        else:
            # Setup completo
            success = setup.setup_complete_environment()
            
            if success:
                print("\n🎉 SETUP CONCLUÍDO COM SUCESSO!")
                print("🚀 Você pode agora executar: python main.py")
            else:
                print("\n❌ SETUP FALHOU")
                print("💡 Verifique os logs acima para detalhes")
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n⏹️ Setup interrompido pelo usuário")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Erro inesperado: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
